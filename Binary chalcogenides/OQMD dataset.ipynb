metals=[    'Sc', 'Ti', 'V', 'Cr', 'Mn', 'Fe', 'Co', 'Ni',
    'Cu', 'Zn', 'Y', 'Zr', 'Nb', 'Mo', 'Tc', 'Ru',
    'Rh', 'Pd', 'Ag', 'Cd', 'Hf', 'Ta', 'W', 'Re',
    'Os', 'Ir', 'Pt', 'Au', 'Hg']
chalcogens=['S','Se','Te']


chemsys=[]
for metal in metals:
        for chalc in chalcogens:
                chemsys.append({f'{metal}-{chalc}'})
len(chemsys)

api = 'h79cBkPf4hpT24odYyHNpGTRNWuuPh2E'


from mp_api.client import MPRester

docs=[]
with MPRester(api) as mpr:
    for chem in chemsys:
        doc=mpr.materials.summary.search(
            chemsys=chem,
            formula='AB2',
            fields=['formula_pretty','material_id','band_gap','symmetry','structure','warnings']
        )
        list_of_available_fields = mpr.materials.summary.available_fields

        docs.append(doc)

fin_docs=[]
for doc in docs:
    fin_docs+=doc

