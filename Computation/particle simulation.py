import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation

class LennardJonesMD:
    def __init__(self, n_particles=64, box_size=8.0, dt=0.001, temperature=1.0):
        self.n_particles = n_particles
        self.box_size = box_size
        self.dt = dt
        self.temperature = temperature
        self.epsilon = 1.0
        self.sigma = 1.0
        self.cutoff = 2.5 * self.sigma
        self.positions = self.initialize_fcc_lattice()
        self.velocities = self.initialize_maxwell_boltzmann_velocities()
        self.forces = np.zeros((n_particles, 2))
        self.kinetic_energies = []
        self.potential_energies = []
        self.total_energies = []
    def initialize_fcc_lattice(self):
        density = self.n_particles / (self.box_size ** 2)
        lattice_constant = np.sqrt(4.0 / density)
        n_cells = int(np.ceil(np.sqrt(self.n_particles / 4)))
        lattice_constant = self.box_size / n_cells
        positions = []
        particle_count = 0
        for i in range(n_cells):
            for j in range(n_cells):
                if particle_count >= self.n_particles:
                    break
                base_x = i * lattice_constant
                base_y = j * lattice_constant
                fcc_positions = [
                    [0.0, 0.0],
                    [0.5 * lattice_constant, 0.0],
                    [0.0, 0.5 * lattice_constant],
                    [0.5 * lattice_constant, 0.5 * lattice_constant]
                ]
                for pos in fcc_positions:
                    if particle_count >= self.n_particles:
                        break
                    x = (base_x + pos[0]) % self.box_size
                    y = (base_y + pos[1]) % self.box_size
                    positions.append([x, y])
                    particle_count += 1
            if particle_count >= self.n_particles:
                break
        return np.array(positions[:self.n_particles])
    def initialize_maxwell_boltzmann_velocities(self):
        velocities = np.random.normal(0, np.sqrt(self.temperature), (self.n_particles, 2))
        velocities -= np.mean(velocities, axis=0)
        current_temp = self.calculate_temperature(velocities)
        if current_temp > 0:
            scale_factor = np.sqrt(self.temperature / current_temp)
            velocities *= scale_factor
        return velocities
    def calculate_temperature(self, velocities=None):
        if velocities is None:
            velocities = self.velocities
        kinetic_energy = 0.5 * np.sum(velocities ** 2)
        temperature = kinetic_energy / self.n_particles
        return temperature
    def apply_periodic_boundary(self, positions):
        return positions % self.box_size
    def minimum_image_distance(self, r1, r2):
        dr = r1 - r2
        dr = dr - self.box_size * np.round(dr / self.box_size)
        return dr
    def calculate_forces_and_potential(self):
        self.forces.fill(0.0)
        potential_energy = 0.0
        for i in range(self.n_particles):
            for j in range(i + 1, self.n_particles):
                dr_vec = self.minimum_image_distance(self.positions[i], self.positions[j])
                r = np.linalg.norm(dr_vec)
                if r < self.cutoff and r > 0.01:
                    r6_inv = (self.sigma / r) ** 6
                    r12_inv = r6_inv ** 2
                    potential = 4.0 * self.epsilon * (r12_inv - r6_inv)
                    potential_energy += potential
                    force_magnitude = 24.0 * self.epsilon * (2.0 * r12_inv - r6_inv) / r
                    force_vec = force_magnitude * dr_vec / r
                    self.forces[i] += force_vec
                    self.forces[j] -= force_vec
        return potential_energy
    def calculate_kinetic_energy(self):
        return 0.5 * np.sum(self.velocities ** 2)
    def velocity_verlet_step(self):
        self.positions += self.velocities * self.dt + 0.5 * self.forces * self.dt**2
        self.positions = self.apply_periodic_boundary(self.positions)
        forces_old = self.forces.copy()
        potential_energy = self.calculate_forces_and_potential()
        self.velocities += 0.5 * (forces_old + self.forces) * self.dt
        kinetic_energy = self.calculate_kinetic_energy()
        total_energy = kinetic_energy + potential_energy
        current_temperature = self.calculate_temperature()
        self.kinetic_energies.append(kinetic_energy)
        self.potential_energies.append(potential_energy)
        self.total_energies.append(total_energy)
        return kinetic_energy, potential_energy, total_energy, current_temperature

def main():
    sim = LennardJonesMD(n_particles=64, box_size=8.0, dt=0.001, temperature=1.5)
    fig, ax = plt.subplots(1, 1, figsize=(10, 10))
    ax.set_xlim(0, sim.box_size)
    ax.set_ylim(0, sim.box_size)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    particles = ax.scatter(sim.positions[:, 0], sim.positions[:, 1],
                          s=40, c='blue', alpha=0.8, edgecolors='black', linewidth=0.5)
    info = ax.text(0.02, 0.98, '', transform=ax.transAxes,
                  verticalalignment='top', fontsize=10,
                  bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))
    def animate(frame):
        ke, pe, te, temp = sim.velocity_verlet_step()
        particles.set_offsets(sim.positions)
        info.set_text(f'Step: {frame}\n'
                     f'Temperature: {temp:.3f}\n'
                     f'Kinetic Energy: {ke:.3f}\n'
                     f'Potential Energy: {pe:.3f}\n'
                     f'Total Energy: {te:.3f}')
        return particles, info
    anim = animation.FuncAnimation(fig, animate, frames=10000, interval=10,
                                   blit=False, repeat=True)
    plt.tight_layout()
    anim.save('particle_simulation.gif', writer='pillow', fps=100)
    plt.show()

if __name__ == "__main__":
    main()