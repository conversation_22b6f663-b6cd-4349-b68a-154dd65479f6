import tarfile as tf 
from glob import glob as g
import zipfile as zp
import pandas as pd
import json

m_paths=g(r'/home/<USER>/Coding/Machine learning on chalcogen perovskties/Datasets/*/*/VASP_Output_data/*/bandgap.json')
poscars=g(r'/home/<USER>/Coding/Machine learning on chalcogen perovskties/Datasets/*/*/VASP_Output_data/*/POSCAR')
len(m_paths)

Se_data=pd.DataFrame()
Se_data['Paths']=m_paths

def get_info(path,prop):
    with open(path) as f:
        data=json.load(f)
        return data[prop]

def get_elem(path):
    pos_path=path.replace('bandgap.json','POSCAR')
    with open(pos_path) as f:
        lines=f.readlines()
        elems=(lines[0]).split()
        return elems

get_elem(poscars[0])

Se_data['Formula']=Se_data['Paths'].apply(lambda x: get_info(x,'Formula'))
Se_data['Prototype']=Se_data['Paths'].apply(lambda x: get_info(x,'Prototype'))
Se_data['Lengths']=Se_data['Paths'].apply(lambda x: get_info(x,'lengths'))
Se_data['Bandgap']=Se_data['Paths'].apply(lambda x: get_info(x,'bandgap'))
Se_data[['a','b','c']]=Se_data['Lengths'].tolist()
Se_data.drop('Lengths',axis=1,inplace=True)

Se_data

import re

element_pattern = r'[A-Z][a-z]?'
elements=Se_data['Formula'].apply(lambda x: re.findall(element_pattern,x))
Se_data['n_elem']=elements.apply(len)


Se_final = Se_data[Se_data.n_elem == 3].copy()
Se_final[['A', 'B', 'C']] = Se_final['Paths'].apply(get_elem).tolist()



Se_final.C.value_counts()

from ase.db import connect

db=connect(r'/home/<USER>/Coding/Machine learning on chalcogen perovskties/abs3.db')

S_data=pd.DataFrame()
S_data['rows']=list(db.select())

import numpy as np
S_data['Formula']=S_data['rows'].apply(lambda x: x.formula)
S_data['Prototype'] = S_data['rows'].apply(lambda x: x.prototype if hasattr(x, 'prototype') else np.nan)
S_data['Bandgap'] = S_data['rows'].apply(lambda x: x.PBEsol_gap if hasattr(x, 'PBEsol_gap') else np.nan)

S_data=S_data.dropna()

S_data[['a','b','c']]=S_data['rows'].apply(lambda x: x.toatoms().get_cell().lengths()).tolist()

S_data

def get_unique_elements(atoms):
    elems, seen = atoms.get_chemical_symbols(), set()
    unique_elems = []
    counts = {'A': 0, 'B': 0, 'C': 0}
    for x in elems:
        if x not in seen:
            unique_elems.append(x)
            seen.add(x)
        if x in counts:
            counts[x] += 1
    return [unique_elems, counts['A'], counts['B'], counts['C']]

S_elements=S_data['rows'].apply(lambda x: re.findall(element_pattern,x.toatoms().get_chemical_formula()))
S_elements.apply(len).value_counts()
S_final=S_data[S_elements.apply(len)==3].copy()
S_final[['A','B','C','nA','nB','nC']]=S_final['rows'].apply(lambda x: get_unique_elements(x.toatoms())).tolist()

S_final

S_final=S_final[S_final.C=='S']

S_final.shape

cols=['Formula','Prototype','Bandgap','a','b','c','A','B','C']
fin_data=pd.concat([S_final[cols],Se_final[cols]])

fin_data

fin_data.to_csv('Final dataset for chalcogen perovskites.csv')

