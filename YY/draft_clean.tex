\documentclass[12pt,journal,onecolumn]{IEEEtran}
\IEEEoverridecommandlockouts
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{float}
\usepackage{array}
\usepackage{times}
\usepackage[T1]{fontenc}

\title{Crystal Systems Classification Using Machine Learning for Phosphate Based Cathode Materials in Lithium-Ion Battery}


\usepackage{tabularx} % add this in your preamble if not already included

\author{%
\small
\setlength{\tabcolsep}{12pt} % adjust column spacing
\begin{center}
\begin{tabularx}{\textwidth}{>{\centering\arraybackslash}X >{\centering\arraybackslash}X >{\centering\arraybackslash}X >{\centering\arraybackslash}X}
\textbf{Yogesh Yadav} & \textbf{Dr. Sandeep K Yadav} & \textbf{Dr. Vivek <PERSON>} & \textbf{Dr. Ambesh Dixit} \\
Dept. Physics & Dept. Electrical Engineering & Dept. Mathematics & Dept. Physics \\
Indian Institute of Technology Jodhpur & Indian Institute of Technology Jodhpur & Indian Institute of Technology Jodhpur & Indian Institute of Technology Jodhpur \\
Jodhpur, India & Jodhpur, India & Jodhpur, India & Jodhpur, India \\
\texttt{<EMAIL>} & \texttt{<EMAIL>} & \texttt{<EMAIL>} & \texttt{<EMAIL>} \\
\end{tabularx}
\end{center}
}
\begin{document}

\maketitle

\begin{abstract}
The physical and chemical characteristics of cathodes used in batteries are derived from the lithium-ion phosphate cathodes' crystalline arrangement, which is pivotal to the overall battery performance. Therefore, the correct prediction of the crystal system is essential to estimate the properties of cathodes. This study investigates machine learning classification algorithms for predicting the crystal systems, namely monoclinic, orthorhombic, and triclinic, related to Li–P– (Mn, Fe, Co, Ni, V)–O based Phosphate cathodes. The data used in this work is extracted from the materials project. Feature evaluation emphasizes that cathode properties depend on the crystal structure, and optimized classification strategies lead to better predictability. Ensemble machine learning algorithms such as Random Forest, Extremely Randomized Trees, and Gradient Boosting Machines have demonstrated the best predictive capabilities for crystal systems under the Monte Carlo cross-validation test. Additionally, sequential forward selection (SFS) is performed to identify the most critical features influencing the prediction accuracy for different machine learning models, with Volume, Band gap, and Sites as input features ensemble machine learning algorithms such as Random Forest (80.69\%), Extremely Randomized Tree(78.96\%), and Gradient Boosting(80.69\%) provide the maximum accuracy.
\end{abstract}

\noindent\textbf{Keywords:} Crystal system, Ensemble machine learning, Monte Carlo cross-validation, sequential forward selection

\section{Introduction}
Lithium-ion batteries (LiBs) play a pivotal role in modern energy storage technologies, powering various applications, from portable electronics to electric vehicles~\cite{ahsan2022} and grid-scale energy storage~\cite{chen2020}. The performance, efficiency, and longevity of LiBs are significantly influenced by the properties of their cathode materials, which are often governed by their crystal structure~\cite{chen2024}. Understanding and predicting the crystal system of cathode materials is crucial because the arrangement of atoms in a crystal lattice directly affects key characteristics of the material, such as ion diffusion pathways~\cite{zhang2013}, electrical conductivity~\cite{cai2021} and stability under operating conditions~\cite{de2019}. The selection and optimization of cathode materials have traditionally relied on experimental techniques, such as X-ray diffraction (XRD)~\cite{duncan2011}, and computational methods, such as density functional theory (DFT)~\cite{kalantarian2013}, to determine their crystal systems. Although these methods provide accurate results, they are often resource-intensive and time-consuming.

Machine learning (ML) provides a data-driven approach to identifying patterns/ trends and making predictions, enabling researchers to address complex problems that are challenging to solve through conventional methods~\cite{mobarak2023scope}. For LiB cathode materials, ML has proven to be a promising tool in predicting critical materials' properties, including the crystal system, based on input features such as chemical composition and thermodynamic properties~\cite{shandiz2016application}. Today, researchers have access to a huge amount of information about the predicted properties of materials. The schematics of the machine learning framework is explained in Fig.~\ref{fig:workflow} explaining the three components: (i) data processing, (ii) model training, and (iii) feature selections. For example, the Materials Project provides a free, web-based platform where anyone can explore the physical and chemical properties of both known and predicted materials. These properties are calculated using DFT, which helps in estimating physical properties such as crystallographic structure and electronic bandgap of materials~\cite{jain2011high,erum2017mechanical,jain2013jain}. Improvements in exchange-correlation potentials have made it possible to accurately calculate the physical properties of many different materials, including those used in lithium-ion batteries~\cite{yan2014review,zhao2022first,ceder1997application,ong2011first}.

Phosphate-based cathode materials with Li–P–(V, Mn, Fe, Co, Ni)–O compositions are of great interest for research due to their high capacity (volumetric and gravimetric) and stability~\cite{hautier2011phosphates,padhi1997phospho}. This study utilizes various classification algorithms to predict the crystal systems (Monoclinic, Triclinic, and Orthorhombic) of cathode materials with Li–P–(V, Mn, Fe, Co, Ni)–O compositions based on data obtained from the Materials Project. Machine learning models, including Linear Discriminant Analysis (LDA), Support Vector Machines (SVM), K-Nearest Neighbors (KNN), and ensemble methods such as Random Forest (RF), Extremely Randomized Trees (ERT), and Gradient Boosting Machines (GBM), were employed for this task. Monte Carlo Cross-Validation (MCCV) was conducted to ensure reliable performance evaluation. A sequential forward selection (SFS) approach was employed to enhance model interpretability and performance and identify each machine learning model's the best three most influential features. These key features were used to construct feature subsets, and models were retrained and evaluated with different feature combinations. This approach identified optimal feature sets for achieving high prediction accuracy and provided valuable insight into the role of specific cathode properties in influencing the crystal system.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.6\columnwidth]{flowchart for model.png}
    \caption{Machine learning Framework for Data Processing, Model Training, and Feature Selection}
    \label{fig:workflow}
\end{figure}

\section{Methodology}
\subsection{The dataset}
The dataset consists of results from DFT calculations for one thousand eight hundred nineteen (1819) cathode materials with Li–P–(V, Mn, Fe, Co, Ni)–O compositions derived from the Materials Project. The Materials Project generates data using advanced computational tools and methods, leveraging first-principles density functional theory (DFT) framework using a generalized gradient approximation (GGA) functional parametrized by Perdew Burke and Ernzerhof (PBE)~\cite{jain2013commentary,perdew1996generalized}. The transition metals, Fe, Co, Mn, Ni, and V have been assigned a U parameter to correct for the self-interaction part in GGA~\cite{anisimov1991band,anisimov1997first}.

The DFT calculations and optimizations in the Materials Project are carried out using VASP software~\cite{kresse1996efficiency}. The selected materials are symbolically presented in Table~\ref{tab:materials} with respective materials parameters used in the present work. The data set includes attributes such as chemical formula, space group symbol, space group number, formation energy ($E_f$), energy above hull ($E_H$), bandgap ($E_g$), number of atomic sites ($N_s$), density ($\rho$), unit cell volume ($V$) and crystal system.

The parameters $N_s$ and $\rho$ represent the number of atoms in a unit cell and the density of the bulk crystalline material, respectively. Machine learning models in this context often use $V=M/\rho$ where $M$ is the molar mass, to predict material properties. The formation energy $E_f$ indicates the thermodynamic stability of a material relative to its constituent elements, while the energy above hull $E_H$ represents the energy difference between a material and the most stable phase at the same composition. The bandgap $E_g$ is a critical electronic property that determines the electrical conductivity of the material. These features collectively provide a comprehensive description of the materials' structural, thermodynamic, and electronic properties, making them suitable for machine learning-based crystal system classification.



\begin{table}[H]
\centering
\caption{Dataset of selected phosphate materials with properties used in the study.}
\label{tab:materials}
\setlength{\tabcolsep}{8pt} % moderate column spacing
\renewcommand{\arraystretch}{1.8} % slightly taller rows for clarity
\footnotesize
\begin{tabular}{l c c c c c c c c l}
\hline
\textbf{Formula} & \textbf{Space} & \textbf{SG} & \textbf{Sites} & \textbf{$E_H$} & \textbf{$E_F$} & \textbf{Volume} & \textbf{Density} & \textbf{Band} & \textbf{Crystal} \\
 & \textbf{Group} & \textbf{No.} & \textbf{($N_s$)} & \textbf{(eV)} & \textbf{(eV)} & \textbf{(\AA$^3$)} & \textbf{(g/cm$^3$)} & \textbf{Gap (eV)} & \textbf{System} \\
\hline
Li$_2$FeP$_2$O$_7$ & P-1 & 2 & 48 & 0.033 & -2.539 & 587.6 & 2.754 & 4.355 & Triclinic \\
Li$_2$VP$_4$O$_{13}$ & P-1 & 2 & 40 & 0.046 & -2.616 & 537.3 & 2.452 & 1.829 & Triclinic \\
Li$_6$Ni$_5$(P$_2$O$_7$)$_4$ & P-1 & 2 & 94 & 0.038 & -2.407 & 1049.6 & 3.261 & 3.481 & Triclinic \\
Li$_2$V$_3$(PO$_4$)$_3$ & C2 & 5 & 40 & 0.060 & -2.600 & 474.4 & 3.162 & 0.449 & Monoclinic \\
Li$_3$Mn$_2$(PO$_3$)$_7$ & P-1 & 2 & 66 & 0.083 & -2.558 & 846.6 & 2.681 & 4.012 & Triclinic \\
\vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots & \vdots \\
LiCo(PO$_3$)$_3$ & P2$_1$2$_1$2$_1$ & 19 & 56 & 0.000 & -2.499 & 640.9 & 3.138 & 2.788 & Orthorhombic \\
\hline
\end{tabular}
\end{table}

The dataset contains 1819 materials, with 1,095 triclinic, 459 monoclinic, and 265 orthorhombic crystal systems. The distribution of crystal systems is shown in Fig.~\ref{fig:bar_plt}.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.6\columnwidth]{barplot for number of crystal system.png}
    \caption{Bar plot showing the distribution of crystal systems}
    \label{fig:bar_plt}
\end{figure}

\subsection{Data Visualization and Analysis}
The pairs plot in Fig.~\ref{fig:pair_plt} illustrates the relationships between different properties of Li–(Mn, Fe, Co, Ni, V)–P–O cathodes. The diagonal elements show the distribution of individual features, while the off-diagonal elements display scatter plots between pairs of features, colored by crystal system.

\begin{figure}[H]
    \centering
    \includegraphics[width=\columnwidth]{pair.png}
    \caption{The pairs plot of different properties of Li– (Mn, Fe, Co, Ni, V)–P–O cathodes based on the extracted data from the Materials Project.}
    \label{fig:pair_plt}
\end{figure}

Box plots in Fig.~\ref{fig:box_plt} provide insights into the distribution and variability of each feature across different crystal systems. The correlation analysis, represented as a heatmap in Fig.~\ref{fig:box_plt}(f), reveals the linear relationships between features.

\begin{figure}[H]
    \centering
    \includegraphics[width=\columnwidth]{box1.png}
    \includegraphics[width=\columnwidth]{box2.png}
    \includegraphics[width=\columnwidth]{box3.png}
    \caption{(a-e)Box plot and (f) Correlation analysis is represented as a heatmap.}
    \label{fig:box_plt}
\end{figure}

\subsection{Data Preprocessing}
Outlier detection and elimination were performed using the Interquartile Range (IQR) method, as shown in Fig.~\ref{fig:IQR}. This preprocessing step helps improve model performance by removing data points that may negatively impact the learning process.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.6\columnwidth]{IQR.png}
    \caption{Outlier Detection and Elimination via Interquartile Range}
    \label{fig:IQR}
\end{figure}

\subsection{Machine Learning Models}
Six different machine learning algorithms were employed for crystal system classification. Fig.~\ref{fig:models} shows the schematic representation of these models.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.48\columnwidth]{ldasvm.png}
    \includegraphics[width=0.48\columnwidth]{knnrf.png}
    \includegraphics[width=0.48\columnwidth]{ERT.png}
    \includegraphics[width=0.48\columnwidth]{GBM.png}
    \caption{Schematic Representation of Machine Learning Models including (a) Linear Discriminant Analysis (LDA) for classification and dimensionality reduction, (b) Support Vector Machine (SVM) for optimal hyperplane-based separation, (c) k-Nearest Neighbors (K-NN) for instance-based classification, (d) Random Forest (RF) for ensemble-based decision-making, (e) Extremely Randomized Trees (Extra Trees) for enhanced randomness in tree construction, and (f) Gradient Boosting Machine (GBM) for iterative error minimization and improved predictive performance.}
    \label{fig:models}
\end{figure}

\subsubsection{Linear Discriminant Analysis (LDA)}
Linear Discriminant Analysis (LDA), Fig.~\ref{fig:models}(a) is a dimensionality reduction and classification technique that projects data onto a lower-dimensional space while maximizing class separability~\cite{sharma2015linear}. LDA assumes that the data follows a Gaussian distribution and that all classes have the same covariance matrix~\cite{xanthopoulos2013linear}.

For this study, LDA was applied to reduce the dimensionality of the feature space while preserving the discriminative information necessary for crystal system classification. The algorithm seeks to find a linear combination of features that best separates the three crystal systems (monoclinic, triclinic, and orthorhombic). The regularization parameter was set to auto to automatically determine the optimal shrinkage parameter~\cite{tharwat2017linear}.

\subsubsection{Support Vector Machine (SVM)}
Support Vector Machine (SVM), Fig.~\ref{fig:models}(b) is a powerful classification algorithm that finds the optimal hyperplane to separate different classes by maximizing the margin between them~\cite{jakkula2006tutorial}. SVM can handle both linear and non-linear classification problems through the use of kernel functions~\cite{yue2003svm}.

\subsubsection{k-Nearest Neighbors (kNN)}
K-Nearest Neighbors (kNN), Fig.~\ref{fig:models}(c) is a simple, non-parametric classification algorithm that assigns a class label based on the majority class among its K nearest neighbors~\cite{altman1992introduction}. The model's performance depends on the choice of K and the distance metric~\cite{hechenbichler2004weighted}.

For this study, we used K = 3, i.e., classification was based on the three closest neighbors. The Euclidean distance metric was used to measure proximity, and uniform weighting was applied to all neighbors. These hyperparameter choices ensured a balance between model complexity and classification accuracy.

\subsubsection{Random Forest (RF)}
Random Forest (RF), Fig.~\ref{fig:models}(d) is an ensemble learning method that combines multiple decision trees to improve prediction accuracy and reduce overfitting~\cite{breiman2001random}. Each tree in the forest is trained on a random subset of the data and features, and the final prediction is made by majority voting~\cite{ali2012random}.

\subsubsection{Extremely Randomized Trees (ERT)}
Extremely Randomized Trees (ERT), Fig.~\ref{fig:models}(e) is an ensemble method similar to Random Forest but with additional randomness in the tree construction process~\cite{geurts2006extremely}. Instead of finding the best split at each node, ERT randomly selects both the features and the split thresholds, which can lead to better generalization in some cases.

\subsubsection{Gradient Boosting Machine (GBM)}
Gradient Boosting Machine (GBM), Fig.~\ref{fig:models}(f) is an ensemble method that builds models sequentially, where each new model corrects the errors made by the previous models~\cite{natekin2013gradient}. This iterative approach often leads to high predictive accuracy~\cite{ayyadevara2018gradient}.

\section{Results and Discussion}
The data set was divided into 80\% for training and 20\% for testing purposes. The 80\% training data was utilized to train multiple machine learning models, including LDA, SVM, KNN, RF, ERT, and GBM, followed by hyperparameter tuning. Among these models, the GBM, ERT, and RF models achieved the highest test accuracy: 74.06\%, 74.93\%, and 76.37\%, respectively, on the 20\% test data because GBM, ERT, and RF are ensemble models; they outperform simpler models like LDA by capturing nonlinear relationships and feature interactions in the data. Unlike LDA, which assumes linear boundaries and Gaussian distributions, these models make no such assumptions and are robust to noise and outliers. Their ability to aggregate predictions from multiple decision trees improves generalization and accuracy, making them particularly effective for complex and high-dimensional data sets.

Fig.~\ref{fig:confusion_matrix} summarizes the performance of different machine learning models by classifying data into three classes (0, 1, and 2) using confusion matrices. Here, the labels 0, 1, and 2 represent the monoclinic, triclinic, and orthorhombic crystal systems. These matrices illustrate the alignment between the predicted and actual class labels, providing a detailed classification accuracy assessment for each class.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.9\columnwidth]{confusion.png}
    \caption{Confusion Matrix for the different models where 0,1 and 2 correspond to monoclinic, triclinic, and orthorhombic crystal systems, respectively.}
    \label{fig:confusion_matrix}
\end{figure}

\subsection{Monte Carlo Cross-Validation}
The Fig.~\ref{fig:mccv_model} shows the relationship between the percentage of training data and mean accuracy for six machine learning models using MCCV. GBM and ERT consistently achieve the highest accuracy, while Linear Discriminant Analysis (LDA) performs the least among all. Increasing the training percentage improves the accuracy for all models, but the gains diminish as it approaches 90\%. Ensemble models like GBM and ERT exhibit robust performance and generalization compared to others, highlighting their effectiveness for the dataset. Fig.~\ref{fig:std} shows that Extra Trees (ERT) and Gradient Boosting (GBM) exhibit the lowest standard deviation, indicating the most consistent and stable performance across different Monte Carlo Cross-Validation (MCCV) splits. This reflects their robustness to variations in train-test splits and their ability to generalize well to unseen data. Their ensemble-based mechanisms effectively minimize variability, making them reliable models for this task. Lower std for these models implies higher dependability in predictive performance.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.6\columnwidth]{MCCV.png}
    \caption{Effect of percentage of training data for building ML models on the mean accuracy.}
    \label{fig:mccv_model}
\end{figure}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.6\columnwidth]{Std.png}
    \caption{Curve between training data percentage Vs standard deviation in MCCV}
    \label{fig:std}
\end{figure}

\subsection{Sequential Forward Selection}
Following Monte Carlo Cross-Validation, Fig.~\ref{fig:sfs_model} Sequential Forward Selection (SFS) enhanced model performance and interpretability by identifying the most significant input features.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\columnwidth]{sequential fs.png}
    \caption{Schematic representation of forward sequential feature selection}
    \label{fig:sfs_model}
\end{figure}

Fig.~\ref{fig:feature_importance} shows the top features and their importance for the best performing models. The analysis reveals that Volume, Band Gap, and Sites are the most critical features for crystal system classification.

\begin{figure}[H]
    \centering
    \includegraphics[width=1\columnwidth]{sfs important features.png}
    \caption{Top Features and Their Importance for Best Performing Models}
    \label{fig:feature_importance}
\end{figure}

Table~\ref{tab:model_features} presents the machine learning models with their selected features and corresponding accuracy. The results show that different models perform optimally with different feature combinations.

\begin{table}[H]
\centering
\caption{Machine learning models with their selected features and corresponding accuracy.}
\label{tab:model_features}
\setlength{\tabcolsep}{4pt}
\renewcommand{\arraystretch}{1.8}
\small
\begin{tabular}{lcccccc}
\hline
\textbf{Model} & \(\mathbf{E_f}\) & \(\mathbf{E_H}\) & \textbf{Volume} & \textbf{Band Gap} & \textbf{Sites} & \textbf{Accuracy (\%)} \\
\hline
LDA &  & $\checkmark$ &  $\checkmark$ & &$\checkmark$ & 56.48 \\
SVM &  & $\checkmark$ &  &  $\checkmark$ & $\checkmark$ & 66.28 \\
KNN & $\checkmark$ &  & $\checkmark$ &  & $\checkmark$ & 70.32 \\
RF  & $\checkmark$ &  &  $\checkmark$ & & $\checkmark$ & 78.67 \\
ERT &  &  & $\checkmark$ & $\checkmark$ & $\checkmark$ & 78.96 \\
GBM &  &  & $\checkmark$ & $\checkmark$ & $\checkmark$ & 80.40\\
\hline
\end{tabular}
\end{table}

Table~\ref{tab:ml_models_accuracy} shows the accuracy of machine learning models with different input features. The combination of Band Gap, Sites, and Volume provides the best performance for most models.

\begin{table}[H]
\centering
\caption{Accuracy (\%) of Machine Learning Models with Different Input Features.}
\label{tab:ml_models_accuracy}
\setlength{\tabcolsep}{6pt}
\renewcommand{\arraystretch}{1.8}
\small
\begin{tabular}{lcccccc}
\hline
\textbf{Features} & \textbf{LDA} & \textbf{SVM} & \textbf{KNN} & \textbf{RF} & \textbf{ERT} & \textbf{GBM} \\
\hline
$E_H$, Band Gap, Sites & 57.06 & 66.28 & 68.88 & 74.93 & 74.06 & 72.91 \\
$E_f$, Sites, Volume    & 56.48 & 68.30 & 73.49 & 78.67 & 77.52 & 79.54 \\
$E_H$, Sites, Volume    & 56.48 & 71.47 & 71.47 & 76.95 & 77.81 & 80.40 \\
Band Gap, Sites, Volume   & 56.48 & 64.84 & 75.50 & 80.69 & 78.96 & 80.40 \\
\hline
\end{tabular}
\end{table}

Fig.~\ref{fig:con_matrix} shows the performance matrix of different models with Band Gap, Sites, and Volume as input features, demonstrating the effectiveness of this feature combination.

\begin{figure}[H]
    \centering
    \includegraphics[width=\columnwidth]{confusion matrix with best features.png}
    \caption{The performance matrix of different models with Band Gap, Sites, and Volume as input features.}
    \label{fig:con_matrix}
\end{figure}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\columnwidth]{stable_material.png}
    \caption{The number of V based stable cathode materials along with their respective crystal systems.}
    \label{fig:stable_material}
\end{figure}

\section{Conclusion}
This study successfully demonstrated the application of machine learning algorithms for predicting crystal systems of Li–P–(Mn, Fe, Co, Ni, V)–O based phosphate cathode materials. The ensemble methods, particularly Random Forest, Extremely Randomized Trees, and Gradient Boosting Machines, showed superior performance compared to traditional algorithms. The sequential forward selection approach identified Volume, Band Gap, and Sites as the most critical features for accurate crystal system classification. These findings provide valuable insights for the design and optimization of cathode materials in lithium-ion batteries.

\bibliographystyle{unsrt}  % or your chosen style
\bibliography{refernces_ieee}  % without .bib extension

\end{document}
